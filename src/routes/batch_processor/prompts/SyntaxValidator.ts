export const SYNTAX_VALIDATOR = `
🔍 **Lynx语法验证器 - 精简版**

## ⚡ 快速检查清单

### JavaScript语法
□ 字符串引号：外层双引号 + 内层单引号
□ 对象属性：避免双引号嵌套冲突
□ 可选链：使用 this.data?.property 访问数据
□ 方法绑定：在created中绑定this上下文

### TTML语法  
□ 组件标签：使用view/text/image等Lynx组件
□ 特殊字符：< → &lt;, > → &gt;, & → &amp;
□ 标签闭合：view必须用view闭合，不能用div
□ 容器高度：scroll-view设置height: 100vh

### TTSS样式
□ 单位系统：优先使用rpx响应式单位
□ 文本居中：text-align: center + line-height = height
□ 选择器：避免多类选择器(.class1.class2)
□ 属性白名单：只使用允许的CSS属性

### Canvas技术
□ 技术选择：原生Canvas或LightChart，不可混用
□ 初始化：setupCanvas()用于原生，initChart()用于LightChart
□ 绑定方式：attachToCanvasView()或new LynxChart()
□ 销毁处理：onUnload中正确销毁资源

## 🚨 高频错误防范

### 关键错误类型
1. **引号嵌套**：personality: "他是'英雄'" ✅
2. **标签混用**：使用view而非div ✅
3. **Canvas混用**：一个组件只选一种Canvas技术 ✅
4. **字符转义**：文本中的<>符号必须转义 ✅
5. **居中规则**：同时设置text-align和line-height ✅

### 验证方法
- 生成代码前逐项检查上述清单
- 确保每个文件都能正常解析运行
- 验证UI效果符合设计预期

## ✅ 质量保证

### 代码完整性
- index.ttml：UI结构完整
- index.ttss：样式设计完整  
- index.js：交互逻辑完整
- index.json：配置信息完整

### 运行稳定性
- 语法解析无错误
- 数据绑定正确
- 事件处理完整
- 生命周期管理规范

### 用户体验
- 界面渲染正常
- 交互响应及时
- 动画效果流畅
- 兼容性良好

---

**记住**：这些规则是为了确保代码质量，而不是限制创意。在遵守语法规范的基础上，尽情发挥你的设计才华！
`;
