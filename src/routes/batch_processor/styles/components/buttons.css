/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🔘 UNIFIED BUTTON SYSTEM - 统一按钮系统
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 合并了modules/buttons.css + unified-button-patch.css + design-system/buttons.css
 * 提供完整的按钮组件解决方案
 *
 * @version 4.0 - 整合版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces modules/buttons.css + unified-button-patch.css + design-system/buttons.css
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 BASE BUTTON SYSTEM - 基础按钮系统
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 主要按钮 - 优化的柔和金色渐变 */
.btn--primary-gold {
  background: linear-gradient(135deg,
        #fefce8 0%,
        /* 极浅的奶黄色 */
        #fef3c7 20%,
        /* 浅黄色 */
        #fde68a 40%,
        /* 柔和金色 */
        #fcd34d 60%,
        /* 中等金色 */
        #f59e0b 100%
        /* 温和的橙金色 */
      );
  color: #92400e;
  border: 1px solid rgba(251, 191, 36, 0.3);
    box-shadow: 0 3px 12px rgba(251, 191, 36, 0.2),
      0 1px 6px rgba(253, 230, 138, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(251, 191, 36, 0.1);
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
    font-weight: 600;
/* 与蓝色按钮保持一致的字体大小 */
  font-size: 13px;

  /* 与蓝色按钮保持一致的布局属性 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn--primary-gold::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%);
  transition: left 0.6s ease;
  z-index: 1;
}

.btn--primary-gold:hover {
  background: linear-gradient(135deg, #fde68a 0%, #fcd34d 25%, #fbbf24 50%, #f59e0b 75%, #d97706 100%);
  transform: translateY(-2px) scale(1.05);

  box-shadow: 0 8px 25px rgba(251, 191, 36, 0.5),
    0 4px 12px rgba(253, 230, 138, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.8),
    inset 0 -2px 0 rgba(251, 191, 36, 0.3),
    0 0 30px rgba(251, 191, 36, 0.3);
}

.btn--primary-gold:hover::before {
  left: 100%;
}

.btn--primary-gold > * {
  position: relative;
  z-index: 2;
}

/* 金色按钮样式已删除，使用深蓝色btn-gold-simple替代 */

/* 星光效果 - 通过伪元素实现 */
.btn-primary-gold-simple::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.4) 1px, transparent 1px),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.3) 1px, transparent 1px),
    radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.2) 1px, transparent 1px);
  background-size: 30px 30px, 40px 40px, 25px 25px;
  animation: sparkle 3s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

/* 高光扫过效果 - 通过伪元素实现，防止溢出 */
.btn-primary-gold-simple::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.6) 50%, transparent 70%);
  transform: translateX(-100%) skewX(-45deg);
  transition: transform 0.8s ease-out;
  pointer-events: none;
  z-index: 2;
  border-radius: inherit;
  overflow: hidden;
}

.btn-primary-gold-simple:hover::after {
  transform: translateX(100%) skewX(-45deg);
}

/* 星光动画 */
@keyframes sparkle {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* 次要按钮 - 优化的现代玻璃拟态设计 */
.btn--secondary-glass {
  background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.1) 0%,
        rgba(37, 99, 235, 0.15) 50%,
        rgba(29, 78, 216, 0.18) 100%);
  color: #1e40af;
  border: 1px solid rgba(59, 130, 246, 0.25);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);

  /* 优化的尺寸和间距 */
  padding: 10px 18px;
  min-height: 40px;
  border-radius: 12px;

  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.12),
      0 2px 6px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.7);

  text-shadow: none;
  font-weight: 600;
  font-size: 13px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 确保图标和文字对齐 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
  
/* 次要按钮悬停效果 - 柔和的浅蓝色渐变 */
.btn--secondary-glass:hover {
  background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.15) 0%,
      rgba(37, 99, 235, 0.20) 50%,
      rgba(29, 78, 216, 0.25) 100%);
  color: #1d4ed8;
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);

  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.12),
    0 2px 6px rgba(59, 130, 246, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

/* 次要按钮激活效果 */
.btn--secondary-glass:active {
  transform: translateY(-1px) scale(1.01);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* 操作按钮内的徽章优化 */
.btn--secondary-glass .bg-white\/30,
.btn--secondary-glass span[class*="bg-white"] {
  background: rgba(255, 255, 255, 0.25);
  color: inherit;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 700;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-width: 20px;
  text-align: center;
}

.btn--secondary-glass:hover .bg-white\/30,
.btn--secondary-glass:hover span[class*="bg-white"] {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}
  
  /* ═══════════════════════════════════════════════════════════════════════════
                       * 🎯 BUTTON LAYOUT OPTIMIZATION - 按钮布局优化
                       * ═══════════════════════════════════════════════════════════════════════════ */
  
  /* 按钮内容居中对齐 - 通用解决方案 */
  .btn--primary-gold,
  .btn--secondary-glass,
  .btn-primary,
  .btn-secondary {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    vertical-align: middle !important;
  }
  
  /* 按钮内部容器优化 */
  .btn--primary-gold>*,
  .btn--secondary-glass>*,
  .btn-primary>*,
  .btn-secondary>* {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
  }
  
  /* 图标和文字间距优化 */
  .btn--primary-gold .semi-icon,
  .btn--primary-gold svg,
  .btn--secondary-glass .semi-icon,
  .btn--secondary-glass svg {
    margin-right: 6px !important;
    margin-left: 0 !important;
    flex-shrink: 0;
  }
  
  /* 🗑️ 已删除：button-text 容器样式 - 不再使用复杂层级结构 */
  
  /* 进度徽章样式 */
  .btn--primary-gold .progress-badge {
    background: rgba(146, 64, 14, 0.15);
    color: #92400e;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
    margin-left: 6px;
}

/* 确保图标颜色与文字一致 */
.btn--secondary-glass .semi-icon,
.btn--secondary-glass svg {
  color: #1e40af;
    fill: #1e40af;
}

.btn--secondary-glass::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.4) 50%, 
    transparent 100%);
  transition: left 0.5s ease;
  z-index: 1;
}

.btn--secondary-glass:hover {
  background: linear-gradient(135deg, #5fc0e9, #87ceeb 50%, #62bfed);
    border-color: rgba(14, 165, 233, 0.4);
    color: #ffffff;
  transform: translateY(-1px);

  box-shadow: 0 4px 14px rgba(14, 165, 233, 0.25),
      0 2px 6px rgba(14, 165, 233, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* 悬停时图标颜色 */
.btn--secondary-glass:hover .semi-icon,
.btn--secondary-glass:hover svg {
  color: #1d4ed8;
    fill: #1d4ed8;
}

.btn--secondary-glass:hover::before {
  left: 100%;
}

.btn--secondary-glass > * {
  position: relative;
  z-index: 2;
}

/* 所有按钮的基础样式 */
.batch-processor-layout .btn-authority {
  /* 基础布局 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  text-decoration: none;
  outline: none;
  user-select: none;
  box-sizing: border-box;
  
  /* 统一尺寸和间距 */
  padding: var(--space-3) var(--space-4);
  min-height: 40px;
  gap: var(--space-2);
  
  /* 统一边框和圆角 */
  border: none;
  border-radius: var(--radius-lg);
  
  /* 统一字体 */
  font-family: var(--font-family);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  
  /* 统一过渡效果 */
  transition: all var(--duration-normal) var(--ease-out);
  
  /* 防止文本溢出 */
  overflow: hidden;
  text-overflow: ellipsis;
  
  /* 布局控制 */
  contain: layout;
  isolation: isolate;
  position: relative;
  z-index: 4;
}

/* 通用按钮样式 - 兼容现有系统 */
.btn {
  /* 重置默认样式 */
  border: none;
  background: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
  outline: none;
  text-decoration: none;
  
  /* 基础布局 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  /* 基础样式 */
  border-radius: 8px;
  font-weight: 500;
  line-height: 1;
  white-space: nowrap;
  user-select: none;
  
  /* 增强的过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
              transform 0.2s ease-out, 
              box-shadow 0.3s ease-out;
  
  /* hover高光特效基础 */
  position: relative;
  overflow: hidden;
}

/* 通用hover高光特效 */
.btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 
              0 4px 10px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 通用高光扫过效果 - 增强版 */
.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent);
  transition: left 0.8s ease-in-out;
  pointer-events: none;
  z-index: 1;
}

.btn:hover::before {
  left: 100%;
}

/* 主要操作按钮的星光特效 - 仅用于主要按钮 */
.btn--primary-glass::after,
.btn--primary-gold::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.6) 1px, transparent 2px),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.4) 1px, transparent 2px),
    radial-gradient(circle at 40% 70%, rgba(255, 255, 255, 0.5) 1px, transparent 2px),
    radial-gradient(circle at 90% 80%, rgba(255, 255, 255, 0.3) 1px, transparent 2px);
  animation: subtleStarTwinkle 4s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.btn--primary-glass:hover::after,
.btn--primary-gold:hover::after {
  opacity: 1;
  animation-duration: 2s;
}

@keyframes subtleStarTwinkle {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 0.8; }
}

.btn > * {
  position: relative;
  z-index: 2;
}

/* 禁用状态 */
.batch-processor-layout .btn-authority:disabled,
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* 聚焦状态 */
.batch-processor-layout .btn-authority:focus,
.btn:focus-visible {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), 0 4px 12px rgba(59, 130, 246, 0.15);
  outline: none;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📏 BUTTON SIZES - 按钮尺寸
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 超小尺寸 */
.btn-xs {
  height: 24px;
  padding: 0 8px;
  font-size: 0.75rem;
  border-radius: 4px;
  gap: 4px;
  min-width: 48px;
}

/* 小尺寸按钮 */
.batch-processor-layout .btn-authority.btn-sm,
.btn-sm {
  padding: var(--space-2) var(--space-3);
  min-height: 32px;
  height: 32px;
  gap: var(--space-1);
  font-size: var(--font-size-xs);
  border-radius: var(--radius-md);
  min-width: 64px;
}

/* 中等尺寸 - 默认 */
.btn-md {
  height: 40px;
  padding: 0 16px;
  font-size: 0.875rem;
  border-radius: 8px;
  gap: 8px;
  min-width: 80px;
}

/* 大尺寸按钮 */
.batch-processor-layout .btn-authority.btn-lg,
.btn-lg {
  padding: var(--space-3) var(--space-5);
  min-height: 48px;
  height: 48px;
  gap: var(--space-3);
  font-size: var(--font-size-base);
  border-radius: var(--radius-xl);
  min-width: 120px;
}

/* 超大尺寸 */
.btn-xl {
  height: 56px;
  padding: 0 32px;
  font-size: 1.125rem;
  border-radius: 12px;
  gap: 12px;
  min-width: 160px;
}

/* 特定高度按钮 */
.batch-processor-layout .btn-authority.h-14 {
  height: 56px;
  min-height: 56px;
}

.batch-processor-layout .btn-authority.h-16 {
  height: 4rem;
  min-height: 4rem;
  padding: 0 24px;
  font-size: 14px;
  line-height: 1.2;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 BUTTON VARIANTS - 按钮变体
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 主要金色按钮 - 优化版浅金色星光设计 */
.batch-processor-layout .btn-primary-blue,
.btn-primary {
  /* 基础布局 */
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    isolation: isolate;
  
    /* 浅金色渐变背景 */
    background: linear-gradient(135deg,
        #fefce8 0%,
        /* 极浅的奶黄色 */
        #fef3c7 20%,
        /* 浅黄色 */
        #fde68a 40%,
        /* 柔和金色 */
        #fcd34d 60%,
        /* 中等金色 */
        #f59e0b 100%
        /* 温和的橙金色 */
      );
    background-size: 200% 200%;
    color: #92400e;
    border: 1px solid rgba(251, 191, 36, 0.3);
  
    /* 优化的阴影和光效 */
    box-shadow:
      0 4px 16px rgba(251, 191, 36, 0.25),
      0 2px 8px rgba(253, 230, 138, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(251, 191, 36, 0.1);
  
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

/* 删除低优先级的星光效果 - 使用index.css中的高优先级白色星光 */

/* 删除低优先级的光芒扫过效果 - 使用index.css中的高优先级白色光芒扫过 */

/* 删除所有低优先级的悬停和激活状态规则 - 使用index.css中的高优先级规则 */
  
  /* 删除冲突的星光特效 - 使用index.css中的高优先级白色星光 */
  
  @keyframes gentleStarTwinkle {
  
    0%,
    100% {
      opacity: 0.6;
      transform: scale(1);
    }
  
    50% {
      opacity: 0.9;
      transform: scale(1.05);
    }
  }
  
  /* 渐变高光扫过效果 */
  .btn-primary::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.4) 50%,
        transparent 70%);
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    transition: transform 0.8s ease;
    pointer-events: none;
    z-index: 2;
  }
  
  .btn-primary:hover::after {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
  
  /* 确保按钮内容在特效之上 */
  .btn-primary>* {
    position: relative;
    z-index: 3;
  }
  
  /* 删除冲突的emoji星光效果 - 使用index.css中的高优先级白色光芒扫过 */
  
  @keyframes sparkleFloat {
  
    0%,
    100% {
      transform: scale(1.1) rotate(180deg) translateY(0);
      filter: hue-rotate(30deg) brightness(1.2);
    }
  
    50% {
      transform: scale(1.3) rotate(180deg) translateY(-3px);
      filter: hue-rotate(45deg) brightness(1.4);
    }
}

.batch-processor-layout .btn-primary-blue:hover,
.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg,
        #fde68a 0%,
        #fcd34d 25%,
        #fbbf24 50%,
        #f59e0b 75%,
        #d97706 100%);
    background-position: 100% 100%;
  
    box-shadow:
      0 8px 32px rgba(251, 191, 36, 0.4),
      0 4px 16px rgba(253, 230, 138, 0.3),
      inset 0 2px 0 rgba(255, 255, 255, 0.9),
      inset 0 -2px 0 rgba(251, 191, 36, 0.2),
      0 0 40px rgba(251, 191, 36, 0.3);
  
    transform: translateY(-2px) scale(1.02);
}

.batch-processor-layout .btn-primary-blue:active,
.btn-primary:active {
  transform: translateY(-1px) scale(0.98);
    box-shadow:
      0 6px 20px rgba(251, 191, 36, 0.3),
      0 3px 10px rgba(253, 230, 138, 0.2),
      inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 主按钮禁用状态优化 */
.btn-primary:disabled {
  background: linear-gradient(135deg,
      #f3f4f6 0%,
      #e5e7eb 100%) !important;
  color: #9ca3af !important;
  cursor: not-allowed !important;

  box-shadow: 0 2px 4px rgba(156, 163, 175, 0.1) !important;
  transform: none !important;

  /* 禁用所有交互效果 */
  pointer-events: none;
}

.btn-primary:disabled .progress-badge {
  background: rgba(156, 163, 175, 0.2) !important;
  color: #9ca3af !important;
  border-color: rgba(156, 163, 175, 0.3) !important;
}

/* 处理中状态的特殊样式 */
.btn-primary.processing {
  background: linear-gradient(135deg,
      #fef3c7 0%,
      #fde68a 50%,
      #fcd34d 100%) !important;

  /* 添加温和的脉冲动画 */
  animation: processing-pulse 2.5s ease-in-out infinite;
}

@keyframes processing-pulse {

  0%,
  100% {
    box-shadow:
      0 4px 16px rgba(251, 191, 36, 0.3),
      0 2px 8px rgba(253, 230, 138, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transform: scale(1);
  }

  50% {
    box-shadow:
      0 6px 24px rgba(251, 191, 36, 0.45),
      0 3px 12px rgba(253, 230, 138, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      0 0 30px rgba(251, 191, 36, 0.2);
    transform: scale(1.01);
  }
}

/* 处理中状态的星光效果增强 */
.btn-primary.processing::before {
  animation: gentleStarTwinkle 2s ease-in-out infinite, processingShimmer 3s linear infinite;
}

@keyframes processingShimmer {
  0% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.6;
  }
}
/* 次要按钮 */
.btn-secondary {
  background: var(--color-white);
  color: var(--color-primary-600);
  border: 1px solid var(--color-primary-200);
  box-shadow: var(--shadow-button-default);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-primary-50);
  border-color: var(--color-primary-300);
  box-shadow: var(--shadow-button-hover);
}

.btn-secondary:active {
  background: var(--color-primary-100);
}

/* 金色主按钮 - 恢复原版黄色样式 */
.batch-processor-layout .btn-primary-gold,
.batch-processor-layout .btn--primary-gold,
.batch-processor-layout .btn-authority.btn-primary-gold,
button.btn-authority.btn-primary-gold {
  /* 确保正确的布局 */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
  
    /* 原版黄色渐变背景 */
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    background-size: 200% 200%;
    color: #ffffff;
  
    /* 边框和圆角 */
    border: none;
    border-radius: 8px;
  
    /* 阴影效果 */
    box-shadow:
    0 4px 12px rgba(245, 158, 11, 0.3),
      0 2px 6px rgba(251, 191, 36, 0.2);
    
      /* 文字效果 */
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    
      /* 过渡动画 */
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative !important;
  overflow: hidden !important;
  
  /* 修复文字截断和布局问题 */
  white-space: normal !important;
  text-overflow: unset !important;
  padding: 12px 20px !important;
  min-width: 200px !important;
  width: 100% !important;
  max-width: 100% !important;
  flex-shrink: 0 !important;
  font-size: 13px !important;
  font-weight: 600 !important;
  gap: 8px !important;
  letter-spacing: 0.3px !important;

  /* 确保内容正确对齐 */
  text-align: center !important;
  vertical-align: middle !important;
}

/* 金色星光特效 */
.batch-processor-layout .btn-primary-gold::before,
.batch-processor-layout .btn--primary-gold::before,
.batch-processor-layout .btn-authority.btn-primary-gold::before,
button.btn-authority.btn-primary-gold::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 0.9) 2px, rgba(255, 215, 0, 0.5) 4px, transparent 6px),
    radial-gradient(circle at 75% 25%, rgba(255, 215, 0, 0.8) 2px, rgba(255, 248, 220, 0.4) 4px, transparent 6px),
    radial-gradient(circle at 85% 75%, rgba(255, 255, 255, 0.8) 2px, rgba(255, 215, 0, 0.4) 4px, transparent 6px);
  background-size: 100% 100%;
  animation: gentleStarTwinkle 3s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
  border-radius: inherit;
}
/* 金色按钮动态背景动画 */
.batch-processor-layout .btn-primary-gold::after,
.batch-processor-layout .btn--primary-gold::after,
.batch-processor-layout .btn-authority.btn-primary-gold::after,
button.btn-authority.btn-primary-gold::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 70%);
  animation: goldenShimmer 3s ease-in-out infinite;
  pointer-events: none;
  z-index: 2;
}

@keyframes goldenShimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(0%) translateY(0%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

.batch-processor-layout .btn-primary-gold:hover:not(:disabled),
.batch-processor-layout .btn-authority.btn-primary-gold:hover:not(:disabled),
button.btn-authority.btn-primary-gold:hover:not(:disabled) {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  background-position: 100% 0% !important;
  box-shadow:
    0 6px 16px rgba(245, 158, 11, 0.4),
      0 3px 8px rgba(251, 191, 36, 0.3) !important;
      transform: translateY(-1px) scale(1.01) !important;
}

/* 金色按钮点击效果 */
.batch-processor-layout .btn-primary-gold:active,
.batch-processor-layout .btn-authority.btn-primary-gold:active,
button.btn-authority.btn-primary-gold:active {
  transform: translateY(-1px) scale(0.98) !important;
  box-shadow: 
    0 6px 20px rgba(255, 215, 0, 0.4), 
    0 3px 10px rgba(251, 191, 36, 0.3),
    inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.1s ease !important;
}

/* 处理中状态的温和背景动画 */
.batch-processor-layout .btn-primary-gold.processing,
.batch-processor-layout .btn-authority.btn-primary-gold.processing,
button.btn-authority.btn-primary-gold.processing {
  animation: subtleGlow 2s ease-in-out infinite !important;
}

@keyframes subtleGlow {
  0%, 100% {
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.35), 
                0 3px 10px rgba(251, 191, 36, 0.25), 
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }
  50% {
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.45), 
                0 4px 12px rgba(251, 191, 36, 0.35), 
                inset 0 1px 0 rgba(255, 255, 255, 0.5);
  }
}

/* w-full 按钮的宽度处理 */
.batch-processor-layout .btn-primary-gold.w-full,
.batch-processor-layout .btn-authority.btn-primary-gold.w-full,
button.btn-authority.btn-primary-gold.w-full {
  width: 100%;
    max-width: 100%;
    flex-shrink: 1;
}

/* 玻璃主按钮 */
.batch-processor-layout .btn-primary-glass {
  background: var(--gradient-card);
  color: var(--color-primary-700);
  border-color: var(--color-primary-300);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-elevated);
}

.batch-processor-layout .btn-primary-glass:hover {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(232, 244, 253, 0.9) 100%
  );
  border-color: var(--color-primary-400);
  box-shadow: var(--shadow-floating);
}

/* 次要玻璃按钮 */
.batch-processor-layout .btn-secondary-glass {
  background: rgba(255, 255, 255, 0.8);
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);
  backdrop-filter: blur(8px);
  box-shadow: var(--shadow-sm);
}

.batch-processor-layout .btn-secondary-glass:hover {
  background: var(--color-white);
  border-color: var(--color-gray-400);
  box-shadow: var(--shadow-md);
}

/* 成功按钮 */
.btn-success {
  background: linear-gradient(135deg, var(--color-success-500) 0%, var(--color-success-600) 100%);
  color: var(--color-white);
  border: 1px solid var(--color-success-500);
  box-shadow: var(--shadow-green-sm);
}

.btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-success-600) 0%, var(--color-success-700) 100%);
  box-shadow: var(--shadow-green-md);
  transform: translateY(-1px);
}

/* 错误按钮 */
.btn-error {
  background: linear-gradient(135deg, var(--color-error-500) 0%, var(--color-error-600) 100%);
  color: var(--color-white);
  border: 1px solid var(--color-error-500);
  box-shadow: var(--shadow-red-sm);
}

.btn-error:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-error-600) 0%, var(--color-error-700) 100%);
  box-shadow: var(--shadow-red-md);
  transform: translateY(-1px);
}

/* 警告按钮 */
.btn-warning {
  background: linear-gradient(135deg, var(--color-warning-500) 0%, var(--color-warning-600) 100%);
  color: var(--color-white);
  border: 1px solid var(--color-warning-500);
  box-shadow: var(--shadow-amber-sm);
}

.btn-warning:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-warning-600) 0%, var(--color-warning-700) 100%);
  box-shadow: var(--shadow-amber-md);
  transform: translateY(-1px);
}

/* 幽灵按钮 */
.btn-ghost {
  background: transparent;
  color: var(--color-gray-600);
  border: 1px solid var(--color-gray-200);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--color-gray-50);
  border-color: var(--color-gray-300);
  color: var(--color-gray-700);
}

.btn-ghost:active {
  background: var(--color-gray-100);
}

/* 文本按钮 */
.btn-text {
  background: transparent;
  color: var(--color-primary-600);
  border: none;
  padding: 0 8px;
}

.btn-text:hover:not(:disabled) {
  background: var(--color-primary-50);
  color: var(--color-primary-700);
}

.btn-text:active {
  background: var(--color-primary-100);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔧 ICON INTEGRATION - 图标集成增强版
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 图标基础样式 - 增强版 */
.batch-processor-layout .icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  position: relative;
}

/* 图标闪烁效果 */
.icon-flash {
  animation: iconFlash 0.6s ease-out;
}

@keyframes iconFlash {
  0% { transform: scale(1); }
  50% { transform: scale(1.2) rotate(5deg); filter: drop-shadow(0 2px 8px rgba(59, 130, 246, 0.6)); }
  100% { transform: scale(1); }
}

/* 图标脉冲效果 */
.icon-pulse {
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { 
    transform: scale(1); 
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
  50% { 
    transform: scale(1.05); 
    filter: drop-shadow(0 2px 6px rgba(59, 130, 246, 0.4));
  }
}

.batch-processor-layout .icon.size-sm {
  width: 14px;
  height: 14px;
}

.batch-processor-layout .icon.size-md {
  width: 18px;
  height: 18px;
}

.batch-processor-layout .icon.size-lg {
  width: 20px;
  height: 20px;
}

/* 按钮内图标样式 */
.batch-processor-layout .btn-authority .icon {
  flex-shrink: 0;
  width: 18px;
  height: 18px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* 金色按钮中的图标特殊样式 - 与蓝色按钮保持一致的图标大小 */
button.btn--primary-gold svg.h-4,
button.btn--primary-gold svg.w-4,
button.btn--primary-gold svg,
button.btn--primary-gold .icon,
.btn--primary-gold svg.h-4,
.btn--primary-gold svg.w-4,
.btn--primary-gold svg,
.btn--primary-gold .icon,
.batch-processor-layout button.btn--primary-gold svg.h-4,
.batch-processor-layout button.btn--primary-gold svg.w-4,
.batch-processor-layout button.btn--primary-gold svg,
.batch-processor-layout button.btn--primary-gold .icon,
.batch-processor-layout .btn--primary-gold svg.h-4,
.batch-processor-layout .btn--primary-gold svg.w-4,
.batch-processor-layout .btn--primary-gold svg,
.batch-processor-layout .btn--primary-gold .icon,
.batch-processor-layout .btn-primary-gold .icon,
.batch-processor-layout .btn-primary-gold svg,
.batch-processor-layout .btn-authority.btn-primary-gold .icon,
.batch-processor-layout .btn-authority.btn-primary-gold svg,
button.btn-authority.btn-primary-gold .icon,
button.btn-authority.btn-primary-gold svg {
  width: 14px !important;
  height: 14px !important;
  color: #92400e !important;
  filter: none;
  stroke-width: 2.5 !important;
    fill: none !important;
    stroke: #92400e !important;
  display: inline-block;
  vertical-align: middle;
  transform-origin: center center;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  image-rendering: crisp-edges;
  shape-rendering: geometricPrecision;
}

/* 按钮内图标对齐 */
.batch-processor-layout .btn-authority .icon {
  margin: 0;
}

.batch-processor-layout .btn-authority .icon:first-child {
  margin-right: 6px;
}

.batch-processor-layout .btn-authority .icon:last-child {
  margin-left: 6px;
}

/* 悬停时图标动画 */
.batch-processor-layout .btn-authority:hover .icon {
  transform: scale(1.05);
}

/* 处理中状态的图标动画 */
.batch-processor-layout .btn-primary-gold.processing .icon {
  animation: iconProcessingOptimized 2s ease-in-out infinite;
}

@keyframes iconProcessingOptimized {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-1px) scale(1.02);
  }
}

/* 旋转图标特殊处理 */
.batch-processor-layout .btn-primary-gold .animate-spin {
  animation: enhancedSpinOptimized 1.2s linear infinite !important;
  transform-origin: center center !important;
}

@keyframes enhancedSpinOptimized {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 针对特定元素内部图标的旋转动画 - 只旋转图标，不旋转外框 */
#main-content > div > main > div > div > div > div.flex.items-center.justify-between.mb-4 > div > div.icon-container.icon-container--md svg,
#main-content > div > main > div > div > div > div.flex.items-center.justify-between.mb-4 > div > div.icon-container.icon-container--md .icon,
#main-content > div > main > div > div > div > div.flex.items-center.justify-between.mb-4 > div > div.icon-container.icon-container--md .semi-icon,
.icon-container.icon-container--md svg,
.icon-container.icon-container--md .icon,
.icon-container.icon-container--md .semi-icon {
  animation: targetIconSpin 1.2s linear infinite !important;
  transform-origin: center center !important;
  will-change: transform !important;
}

@keyframes targetIconSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 ICON BUTTONS - 图标按钮
 * ═══════════════════════════════════════════════════════════════════════════ */

.btn-icon {
  width: 40px;
  height: 40px;
  min-width: 40px;
  padding: 0;
  border-radius: 50%;
}

.btn-icon.btn-sm {
  width: 32px;
  height: 32px;
  min-width: 32px;
}

.btn-icon.btn-lg {
  width: 48px;
  height: 48px;
  min-width: 48px;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎮 BUTTON GROUPS - 按钮组
 * ═══════════════════════════════════════════════════════════════════════════ */

.btn-group {
  display: inline-flex;
}

.btn-group .btn {
  border-radius: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.btn-group .btn:last-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

.btn-group .btn:not(:last-child) {
  border-right: none;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔄 LOADING STATES - 加载状态
 * ═══════════════════════════════════════════════════════════════════════════ */

.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -8px 0 0 -8px;
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎭 COMPONENT SPECIFIC BUTTONS - 组件专用按钮
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 批处理器页面底部操作按钮 */
.action-button {
  width: 80px;
  height: 32px;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 6px;
  border: 1px solid var(--color-primary-200);
  background: transparent;
  color: var(--color-slate-600);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-button:hover:not(:disabled) {
  border-color: var(--color-primary-300);
  background: var(--color-primary-50);
}

.action-button.primary {
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-700) 100%);
  color: var(--color-white);
  border-color: var(--color-primary-500);
  font-weight: 600;
}

.action-button.primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-800) 100%);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 历史记录按钮 */
.history-pulse-btn {
  height: 40px;
  padding: 0 16px;
  font-size: 0.875rem;
  border-radius: 8px;
  gap: 8px;
  min-width: 80px;
  background: var(--color-white);
  border: 1px solid var(--theme-history-border);
  color: var(--theme-history-primary);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.history-pulse-btn:hover:not(:disabled) {
  background: var(--theme-history-light);
  animation: historyPulse 2s ease-in-out infinite;
  box-shadow: var(--shadow-amber-md);
}

@keyframes historyPulse {
  0%, 100% {
    box-shadow: var(--shadow-amber-md);
  }
  50% {
    box-shadow: var(--shadow-amber-lg);
  }
}

/* 设置按钮 */
.settings-tech-btn {
  height: 40px;
  padding: 0 16px;
  font-size: 0.875rem;
  border-radius: 8px;
  gap: 8px;
  min-width: 80px;
  background: linear-gradient(135deg, var(--theme-settings-bg) 0%, var(--color-white) 100%);
  border: 1px solid var(--theme-settings-border);
  color: var(--theme-settings-primary);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.settings-tech-btn:hover:not(:disabled) {
  background: var(--theme-settings-light);
  box-shadow: var(--shadow-blue-md);
}

/* 提示词抽屉按钮样式 */
.prompt-drawer-button {
  font-size: 0.75rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 6rem;
  text-align: center;
  background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 50%, #ddd6fe 100%);
  border: 1px solid #c4b5fd;
  color: #6b46c1;
  cursor: pointer;
}

.prompt-drawer-button:hover {
  background: linear-gradient(135deg, #fdf4ff 0%, #f3e8ff 30%, #e9d5ff 60%, #ddd6fe 100%);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.25), 0 0 20px rgba(168, 85, 247, 0.15);
  border-color: #a855f7;
  color: #581c87;
}

.prompt-drawer-button:active {
  transform: translateY(0) scale(0.98);
}

.prompt-drawer-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2);
}

/* 白色星光按钮效果 - 惊喜感 */
.btn-white-starlight {
  position: relative;
  overflow: hidden;
  isolation: isolate;
}

/* PromptDrawer 优化 - 简洁高效的高度控制 */

/* CSS自定义属性 - 统一高度管理 */
:root {
  --prompt-drawer-header-height: 60px;
  --prompt-drawer-padding: 32px;
  --prompt-drawer-button-area: 100px;
  --prompt-drawer-textarea-height: calc(100vh - 300px);
}

/* 核心布局类 - 可复用 */
.drawer-fixed-height {
  height: calc(100vh - var(--prompt-drawer-header-height));
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.drawer-content-area {
  flex: 1;
  overflow: hidden;
  padding: 16px;
  padding-bottom: 0;
  display: flex;
  flex-direction: column;
}

.drawer-textarea-container {
  flex: 1;
  min-height: 200px;
  max-height: var(--prompt-drawer-textarea-height);
  overflow-y: auto;
  margin-bottom: 16px;
}

.drawer-button-area {
  flex-shrink: 0;
  padding: 16px;
  background: white;
  border-top: 1px solid #e5e7eb;
  margin-top: auto;
}

/* 响应式优化 - 不同屏幕高度下的动态调整 */
@media (max-height: 900px) {
  :root {
    --prompt-drawer-textarea-height: calc(100vh - 250px);
  }

  .drawer-textarea-container {
    min-height: 180px;
  }

  .drawer-button-area {
    padding: 12px 16px;
  }
}

@media (max-height: 768px) {
  :root {
    --prompt-drawer-textarea-height: calc(100vh - 220px);
  }

  .drawer-textarea-container {
    min-height: 150px;
  }

  .drawer-content-area {
    padding: 12px;
    padding-bottom: 0;
  }

  .drawer-button-area {
    padding: 10px 12px;
  }

  /* 使用提示区域紧凑化 */
  .prompt-drawer .glass-card-gold {
    padding: 8px 12px;
    margin-bottom: 8px;
  }

  .prompt-drawer .glass-card-gold h4 {
    margin-bottom: 8px;
    font-size: 0.875rem;
  }

  .prompt-drawer .glass-card-gold ul {
    font-size: 0.75rem;
    line-height: 1.4;
  }
}

@media (max-height: 600px) {
  :root {
    --prompt-drawer-textarea-height: calc(100vh - 180px);
  }

  .drawer-textarea-container {
    min-height: 120px;
  }

  .drawer-content-area {
    padding: 8px;
    padding-bottom: 0;
  }

  .drawer-button-area {
    padding: 8px;
  }

  /* 极小屏幕下隐藏使用提示，最大化编辑空间 */
  .prompt-drawer .glass-card-gold {
    display: none;
  }

  /* 编辑区域标签紧凑化 */
  .prompt-drawer .glass-card-gold:not(:first-child) {
    display: block;
    padding: 6px 8px;
    margin-bottom: 6px;
  }

  .prompt-drawer .glass-card-gold:not(:first-child) label {
    font-size: 0.8rem;
  }
}

/* 宽屏但高度不足的情况 - 横屏平板等 */
@media (min-width: 1024px) and (max-height: 700px) {
  :root {
    --prompt-drawer-textarea-height: calc(100vh - 200px);
  }

  .drawer-content-area {
    padding: 10px 16px;
    padding-bottom: 0;
  }

  .prompt-drawer .glass-card-gold {
    padding: 8px 12px;
    margin-bottom: 8px;
  }

  .prompt-drawer .glass-card-gold ul {
    font-size: 0.8rem;
    line-height: 1.3;
  }
}
/* 保存按钮浅蓝色高光背景 */
.btn-save-highlight {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
}

/* 保存按钮图标样式 */
.btn-save-highlight .semi-icon {
  color: rgb(30, 64, 175) !important;
  font-size: 14px !important;
  width: 14px !important;
  height: 14px !important;
}
/* 打开所有成功结果按钮的星光效果 */
.open-all-results-btn {
  position: relative;
  overflow: visible;
  /* 允许星光emoji显示在按钮外 */
}

/* 星光emoji样式 */
.open-all-results-btn .starlight-emoji {
  position: absolute;
  top: -4px;
  right: -4px;
  font-size: 18px;
  opacity: 0;
  transform: rotate(0deg) scale(0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  z-index: 10;
}

/* hover时星光出现并旋转闪亮 */
.open-all-results-btn:hover .starlight-emoji {
  opacity: 1;
  transform: rotate(360deg) scale(1.2);
  animation: starlight-sparkle 1.5s ease-in-out infinite;
}

/* 星光闪亮动画 */
@keyframes starlight-sparkle {

  0%,
  100% {
    opacity: 1;
    transform: rotate(360deg) scale(1.2);
    filter: brightness(1) drop-shadow(0 0 4px rgba(255, 215, 0, 0.6));
  }

  25% {
    opacity: 0.7;
    transform: rotate(450deg) scale(1.1);
    filter: brightness(1.3) drop-shadow(0 0 8px rgba(255, 215, 0, 0.8));
  }

  50% {
    opacity: 1;
    transform: rotate(540deg) scale(1.3);
    filter: brightness(1.5) drop-shadow(0 0 12px rgba(255, 215, 0, 1));
  }

  75% {
    opacity: 0.8;
    transform: rotate(630deg) scale(1.1);
    filter: brightness(1.2) drop-shadow(0 0 6px rgba(255, 215, 0, 0.7));
  }
}
/* 覆盖原有的::before效果，使用更高优先级 */
.btn.btn--secondary-glass.btn-white-starlight::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;

  /* 15个白色星点 - 随机分布，不同大小 */
  background-image:
    /* 大星点 - 使用随机动画 */
    radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 0.9) 1px, transparent 2px),
    radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.8) 1.5px, transparent 2.5px),
    radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.9) 1px, transparent 2px),
    radial-gradient(circle at 25% 75%, rgba(255, 255, 255, 0.7) 2px, transparent 3px),
    radial-gradient(circle at 92% 85%, rgba(255, 255, 255, 0.8) 1px, transparent 2px),

    /* 中等星点 */
    radial-gradient(circle at 45% 35%, rgba(255, 255, 255, 0.6) 1px, transparent 1.5px),
    radial-gradient(circle at 90% 60%, rgba(255, 255, 255, 0.8) 1px, transparent 2px),
    radial-gradient(circle at 10% 85%, rgba(255, 255, 255, 0.7) 1px, transparent 1.5px),
    radial-gradient(circle at 60% 20%, rgba(255, 255, 255, 0.9) 1px, transparent 2px),
    radial-gradient(circle at 75% 45%, rgba(255, 255, 255, 0.6) 1px, transparent 1.5px),

    /* 小星点 - 更多细节 */
    radial-gradient(circle at 35% 60%, rgba(255, 255, 255, 0.5) 0.5px, transparent 1px),
    radial-gradient(circle at 80% 40%, rgba(255, 255, 255, 0.6) 0.5px, transparent 1px),
    radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.4) 0.5px, transparent 1px),
    radial-gradient(circle at 55% 85%, rgba(255, 255, 255, 0.7) 0.5px, transparent 1px),
    radial-gradient(circle at 5% 30%, rgba(255, 255, 255, 0.5) 0.5px, transparent 1px);

  /* 多层随机闪烁动画 */
  animation:
    randomTwinkle1 3.2s ease-in-out infinite,
    randomTwinkle2 2.8s ease-in-out infinite 0.5s,
    randomTwinkle3 3.5s ease-in-out infinite 1s;
  opacity: 0.8;
}

/* 白色光芒扫过效果 - 使用更高优先级 */
.btn.btn--secondary-glass.btn-white-starlight::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  pointer-events: none;
  z-index: 11;

  /* 白色光芒渐变 */
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 40%,
    rgba(255, 255, 255, 0.3) 45%,
    rgba(255, 255, 255, 0.6) 50%,
    rgba(255, 255, 255, 0.3) 55%,
    rgba(255, 255, 255, 0.1) 60%,
    transparent 70%
  );

  /* 光芒扫过动画 */
  animation: whiteStarSweep 3.5s ease-in-out infinite;
  opacity: 0.8;
  filter: blur(0.5px);
}

/* 悬停时增强效果 - 使用更高优先级 */
.btn.btn--secondary-glass.btn-white-starlight:hover::before {
  /* 悬停时动画加速，星点变大 */
  animation:
    randomTwinkle1 1.5s ease-in-out infinite,
    randomTwinkle2 1.2s ease-in-out infinite 0.3s,
    randomTwinkle3 1.8s ease-in-out infinite 0.6s;
  opacity: 1;

  /* 悬停时星点变大变亮 */
  background-image:
    radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 1) 2px, transparent 3px),
    radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.9) 2.5px, transparent 3.5px),
    radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 1) 2px, transparent 3px),
    radial-gradient(circle at 25% 75%, rgba(255, 255, 255, 0.8) 3px, transparent 4px),
    radial-gradient(circle at 92% 85%, rgba(255, 255, 255, 0.9) 2px, transparent 3px),
    radial-gradient(circle at 45% 35%, rgba(255, 255, 255, 0.7) 1.5px, transparent 2.5px),
    radial-gradient(circle at 90% 60%, rgba(255, 255, 255, 0.9) 2px, transparent 3px),
    radial-gradient(circle at 10% 85%, rgba(255, 255, 255, 0.8) 1.5px, transparent 2.5px),
    radial-gradient(circle at 60% 20%, rgba(255, 255, 255, 1) 2px, transparent 3px),
    radial-gradient(circle at 75% 45%, rgba(255, 255, 255, 0.7) 1.5px, transparent 2.5px),
    radial-gradient(circle at 35% 60%, rgba(255, 255, 255, 0.6) 1px, transparent 1.5px),
    radial-gradient(circle at 80% 40%, rgba(255, 255, 255, 0.7) 1px, transparent 1.5px),
    radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.5) 1px, transparent 1.5px),
    radial-gradient(circle at 55% 85%, rgba(255, 255, 255, 0.8) 1px, transparent 1.5px),
    radial-gradient(circle at 5% 30%, rgba(255, 255, 255, 0.6) 1px, transparent 1.5px);
}

.btn.btn--secondary-glass.btn-white-starlight:hover::after {
  animation-duration: 1.5s;
  opacity: 1;
}

/* 点击时的惊喜爆发效果 - 使用更高优先级 */
.btn.btn--secondary-glass.btn-white-starlight:active::before {
  animation: surpriseStarBurst 0.6s ease-out;
  opacity: 1;
  transform: scale(1.1);
}

.btn.btn--secondary-glass.btn-white-starlight:active::after {
  animation: whiteStarSweep 0.4s ease-out;
  opacity: 1;
  transform: scale(1.05);
}

/* 确保按钮内容在星光之上 - 使用更高优先级 */
.btn.btn--secondary-glass.btn-white-starlight > * {
  position: relative;
  z-index: 12;
}

/* 强制覆盖原有的::before效果 - 最高优先级 */
.batch-processor-layout .btn.btn--secondary-glass.btn-white-starlight::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: auto !important;
  height: auto !important;
  pointer-events: none !important;
  z-index: 10 !important;

  /* 15个白色星点 - 随机分布，不同大小 */
  background-image:
    /* 大星点 */
    radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 0.9) 1px, transparent 2px),
    radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.8) 1.5px, transparent 2.5px),
    radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.9) 1px, transparent 2px),
    radial-gradient(circle at 25% 75%, rgba(255, 255, 255, 0.7) 2px, transparent 3px),
    radial-gradient(circle at 92% 85%, rgba(255, 255, 255, 0.8) 1px, transparent 2px),

    /* 中等星点 */
    radial-gradient(circle at 45% 35%, rgba(255, 255, 255, 0.6) 1px, transparent 1.5px),
    radial-gradient(circle at 90% 60%, rgba(255, 255, 255, 0.8) 1px, transparent 2px),
    radial-gradient(circle at 10% 85%, rgba(255, 255, 255, 0.7) 1px, transparent 1.5px),
    radial-gradient(circle at 60% 20%, rgba(255, 255, 255, 0.9) 1px, transparent 2px),
    radial-gradient(circle at 75% 45%, rgba(255, 255, 255, 0.6) 1px, transparent 1.5px),

    /* 小星点 */
    radial-gradient(circle at 35% 60%, rgba(255, 255, 255, 0.5) 0.5px, transparent 1px),
    radial-gradient(circle at 80% 40%, rgba(255, 255, 255, 0.6) 0.5px, transparent 1px),
    radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.4) 0.5px, transparent 1px),
    radial-gradient(circle at 55% 85%, rgba(255, 255, 255, 0.7) 0.5px, transparent 1px),
    radial-gradient(circle at 5% 30%, rgba(255, 255, 255, 0.5) 0.5px, transparent 1px) !important;

  /* 多层随机闪烁动画 */
  animation:
    randomTwinkle1 3.2s ease-in-out infinite,
    randomTwinkle2 2.8s ease-in-out infinite 0.5s,
    randomTwinkle3 3.5s ease-in-out infinite 1s !important;
  opacity: 0.8 !important;
  transition: none !important;
}

/* 提示词按钮 */
.prompt-magic-btn {
  height: 40px;
  padding: 0 16px;
  font-size: 0.875rem;
  border-radius: 8px;
  gap: 8px;
  min-width: 80px;
  background: linear-gradient(135deg, var(--theme-prompt-bg) 0%, var(--color-white) 100%);
  border: 1px solid var(--theme-prompt-border);
  color: var(--theme-prompt-primary);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.prompt-magic-btn:hover:not(:disabled) {
  background: var(--theme-prompt-light);
  box-shadow: var(--shadow-purple-md);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * ✨ SPECIAL EFFECTS - 特殊效果按钮增强版
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 🚀 微交互动画效果 */
.btn-micro-bounce:hover {
  animation: microBounce 0.6s ease-out;
}

@keyframes microBounce {
  0% { transform: translateY(0); }
  30% { transform: translateY(-4px); }
  50% { transform: translateY(-2px); }
  70% { transform: translateY(-3px); }
  100% { transform: translateY(0); }
}

/* 🎯 按钮呼吸效果 */
.btn-breathing {
  animation: breathing 3s ease-in-out infinite;
}

@keyframes breathing {
  0%, 100% { 
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
    transform: scale(1.02);
  }
}

/* 💫 按钮磁场效果 */
.btn-magnetic {
  position: relative;
  overflow: hidden;
}

.btn-magnetic::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transition: all 0.3s ease-out;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  z-index: 1;
}

.btn-magnetic:hover::before {
  width: 120%;
  height: 120%;
}

/* 🌟 星光拖尾效果 */
.btn-star-trail {
  position: relative;
  overflow: hidden;
}

.btn-star-trail::after {
  content: '✨';
  position: absolute;
  top: 50%;
  left: 0%;
  transform: translateY(-50%);
  opacity: 0;
  font-size: 12px;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.btn-star-trail:hover::after {
  left: 95%;
  opacity: 1;
  animation: starFloat 0.6s ease-out;
}

@keyframes starFloat {
  0% { transform: translateY(-50%) scale(0.5) rotate(0deg); }
  50% { transform: translateY(-60%) scale(1.2) rotate(180deg); }
  100% { transform: translateY(-50%) scale(1) rotate(360deg); }
}

/* 🔄 涟漪点击效果 */
.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple:active::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: translate(-50%, -50%);
  animation: rippleEffect 0.6s ease-out;
}

@keyframes rippleEffect {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 300px;
    height: 300px;
    opacity: 0;
  }
}

/* 🎨 彩虹渐变动画 */
.btn-rainbow {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #f9ca24, #f0932b, #eb4d4b, #6c5ce7);
  background-size: 300% 300%;
  animation: rainbowShift 3s ease infinite;
}

@keyframes rainbowShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 🔥 热力波动效果 */
.btn-heat-wave:hover {
  animation: heatWave 0.8s ease-in-out;
}

@keyframes heatWave {
  0%, 100% { 
    filter: hue-rotate(0deg) brightness(1); 
    transform: scale(1);
  }
  25% { 
    filter: hue-rotate(10deg) brightness(1.1); 
    transform: scale(1.02);
  }
  50% { 
    filter: hue-rotate(20deg) brightness(1.2); 
    transform: scale(1.04);
  }
  75% { 
    filter: hue-rotate(10deg) brightness(1.1); 
    transform: scale(1.02);
  }
}

/* 发光按钮 */
.btn-glow {
  position: relative;
}

/* ✨ 星光按钮效果 */
.btn-starlight {
  position: relative;
  overflow: hidden;
}

.btn-starlight::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 1) 12px, rgba(255, 255, 255, 0.5) 16px, transparent 20px),
    radial-gradient(circle at 80% 20%, rgba(255, 248, 220, 1) 10px, rgba(255, 248, 220, 0.5) 14px, transparent 18px),
    radial-gradient(circle at 40% 70%, rgba(255, 255, 255, 1) 12px, rgba(255, 255, 255, 0.5) 16px, transparent 20px),
    radial-gradient(circle at 90% 80%, rgba(255, 248, 220, 1) 8px, rgba(255, 248, 220, 0.5) 12px, transparent 16px),
    radial-gradient(circle at 10% 90%, rgba(255, 255, 255, 1) 10px, rgba(255, 255, 255, 0.5) 14px, transparent 18px),
    radial-gradient(circle at 60% 10%, rgba(255, 248, 220, 1) 12px, rgba(255, 248, 220, 0.5) 16px, transparent 20px),
    radial-gradient(circle at 30% 50%, rgba(255, 255, 255, 1) 8px, rgba(255, 255, 255, 0.5) 12px, transparent 16px),
    radial-gradient(circle at 70% 60%, rgba(255, 248, 220, 1) 10px, rgba(255, 248, 220, 0.5) 14px, transparent 18px);
  background-size: 100% 100%;
  animation: starTwinkle 1.5s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
}

.btn-starlight::after {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 300%;
  height: 300%;
  background:
    linear-gradient(45deg,
      transparent 5%,
      rgba(255, 255, 255, 0.2) 20%,
      rgba(255, 255, 255, 0.8) 35%,
      rgba(255, 248, 220, 1) 45%,
      rgba(255, 255, 255, 1) 50%,
      rgba(255, 248, 220, 1) 55%,
      rgba(255, 255, 255, 0.8) 65%,
      rgba(255, 255, 255, 0.2) 80%,
      transparent 95%),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.6) 0%, transparent 70%);
  animation: starSweep 2.5s ease-in-out infinite;
  pointer-events: none;
  z-index: 2;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.8));
}

.btn-starlight:hover::before {
  animation: starTwinkle 0.5s ease-in-out infinite;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 1) 18px, rgba(255, 255, 255, 0.7) 24px, transparent 30px),
    radial-gradient(circle at 80% 20%, rgba(255, 248, 220, 1) 16px, rgba(255, 248, 220, 0.7) 22px, transparent 28px),
    radial-gradient(circle at 40% 70%, rgba(255, 255, 255, 1) 18px, rgba(255, 255, 255, 0.7) 24px, transparent 30px),
    radial-gradient(circle at 90% 80%, rgba(255, 248, 220, 1) 14px, rgba(255, 248, 220, 0.7) 20px, transparent 26px),
    radial-gradient(circle at 10% 90%, rgba(255, 255, 255, 1) 16px, rgba(255, 255, 255, 0.7) 22px, transparent 28px),
    radial-gradient(circle at 60% 10%, rgba(255, 248, 220, 1) 18px, rgba(255, 248, 220, 0.7) 24px, transparent 30px),
    radial-gradient(circle at 30% 50%, rgba(255, 255, 255, 1) 14px, rgba(255, 255, 255, 0.7) 20px, transparent 26px),
    radial-gradient(circle at 70% 60%, rgba(255, 248, 220, 1) 16px, rgba(255, 248, 220, 0.7) 22px, transparent 28px);
  filter: drop-shadow(0 0 15px rgba(255, 255, 255, 1)) drop-shadow(0 0 25px rgba(255, 248, 220, 0.8));
}

.btn-starlight:hover::after {
  animation: starSweep 1.5s ease-in-out infinite;
  background: linear-gradient(45deg,
      transparent 20%,
      rgba(255, 255, 255, 0.3) 35%,
      rgba(255, 255, 255, 1) 45%,
      rgba(255, 248, 220, 1) 50%,
      rgba(255, 255, 255, 1) 55%,
      rgba(255, 255, 255, 0.3) 65%,
      transparent 80%);
}

/* 星光按钮内容层级 */
.btn-starlight>* {
  position: relative;
  z-index: 3;
}

/* 点击时的星光爆发效果 */
.btn-starlight:active::before {
  animation: starTwinkle 0.3s ease-out;
  transform: scale(1.2);
}

.btn-starlight:active::after {
  animation: starSweep 0.5s ease-out;
  background: linear-gradient(45deg,
      transparent 10%,
      rgba(255, 255, 255, 0.6) 25%,
      rgba(255, 255, 255, 1) 40%,
      rgba(255, 248, 220, 1) 50%,
      rgba(255, 255, 255, 1) 60%,
      rgba(255, 255, 255, 0.6) 75%,
      transparent 90%);
}

/* 增强按钮整体发光效果 */
.btn-starlight {
  box-shadow:
    0 4px 12px rgba(251, 191, 36, 0.4),
    0 0 30px rgba(255, 255, 255, 0.3),
    0 0 50px rgba(255, 248, 220, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    /* 添加大型内部星光点 */
    inset 20px 15px 0 -5px rgba(255, 255, 255, 1),
    inset -15px 20px 0 -5px rgba(255, 248, 220, 0.9),
    inset 30px -10px 0 -5px rgba(255, 255, 255, 0.8),
    inset -25px -15px 0 -5px rgba(255, 248, 220, 1),
    inset 10px 30px 0 -5px rgba(255, 255, 255, 0.7),
    inset -30px 5px 0 -5px rgba(255, 248, 220, 0.8);
  animation: buttonPulse 3s ease-in-out infinite;
}

.btn-starlight:hover {
  box-shadow:
    0 8px 30px rgba(251, 191, 36, 0.8),
    0 0 60px rgba(255, 255, 255, 0.6),
    0 0 100px rgba(255, 248, 220, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    /* 悬停时的超大内部星光 */
    inset 25px 20px 0 0px rgba(255, 255, 255, 1),
    inset -20px 25px 0 0px rgba(255, 248, 220, 1),
    inset 35px -15px 0 0px rgba(255, 255, 255, 0.9),
    inset -30px -20px 0 0px rgba(255, 248, 220, 1),
    inset 15px 35px 0 0px rgba(255, 255, 255, 0.8),
    inset -35px 10px 0 0px rgba(255, 248, 220, 0.9);
  transform: translateY(-2px) scale(1.02);
  animation: buttonPulse 1s ease-in-out infinite;
}
.btn-glow:hover:not(:disabled) {
  box-shadow: var(--shadow-button-glow);
}

/* 渐变发光按钮 */
.btn-gradient-glow {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-secondary-500));
  color: var(--color-white);
  border: none;
}

.btn-gradient-glow:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-secondary-600));
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 30px rgba(139, 92, 246, 0.4);
}

/* 火箭发射按钮 */
.btn-rocket {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-orange-500));
  color: var(--color-white);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn-rocket::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.btn-rocket:hover:not(:disabled) {
  animation: rocketLaunch 0.6s ease-out;
  box-shadow: var(--shadow-button-launch);
}

.btn-rocket:hover:not(:disabled)::before {
  left: 100%;
}

@keyframes rocketLaunch {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
  100% {
    transform: translateY(0);
  }
}

/* 高光扫过效果 */
.btn-authority::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent) !important;
  transition: left 0.6s ease-in-out !important;
  z-index: 1 !important;
}

.btn-authority:hover::before {
  left: 100% !important;
}

.btn-authority > * {
  position: relative !important;
  z-index: 2 !important;
}

/* 星光悬停效果 */
.sparkle-hover {
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
}

.sparkle-hover::before {
  content: '' !important;
  position: absolute !important;
  top: -50% !important;
  left: -50% !important;
  width: 200% !important;
  height: 200% !important;
  background: linear-gradient(45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.15) 50%,
      transparent 70%) !important;
  transform: translateX(-100%) translateY(-100%) rotate(45deg) !important;
  transition: transform 0.6s ease !important;
  pointer-events: none !important;
  z-index: 1 !important;
}

.sparkle-hover:hover::before {
  transform: translateX(100%) translateY(100%) rotate(45deg) !important;
}

.sparkle-hover::after {
  content: '✨' !important;
  position: absolute !important;
  top: 8px !important;
  right: 8px !important;
  font-size: 10px !important;
  opacity: 0 !important;
  transform: scale(0.5) rotate(0deg) !important;
  transition: all 0.3s ease !important;
  pointer-events: none !important;
  z-index: 2 !important;
}

.sparkle-hover:hover::after {
  opacity: 0.8 !important;
  transform: scale(1) rotate(180deg) !important;
  animation: sparkleFloat 2s ease-in-out infinite !important;
}

@keyframes sparkleFloat {
  0%, 100% {
    transform: scale(1) rotate(180deg) translateY(0);
  }
  50% {
    transform: scale(1.1) rotate(180deg) translateY(-2px);
  }
}

.sparkle-hover > * {
  position: relative !important;
  z-index: 3 !important;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 TEXT STYLING - 文本样式
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 发光文本效果 */
.glow-text {
  position: relative !important;
  text-shadow: 0 0 6px rgba(255, 255, 255, 0.4) !important;
  transition: all 0.3s ease !important;
}

.glow-text:hover {
  text-shadow:
    0 0 4px rgba(255, 255, 255, 0.8),
    0 0 8px rgba(255, 255, 255, 0.6),
    0 0 12px rgba(255, 255, 255, 0.4) !important;
  animation: textGlow 2s ease-in-out infinite !important;
}

@keyframes textGlow {
  0%, 100% {
    text-shadow:
      0 0 4px rgba(255, 255, 255, 0.8),
      0 0 8px rgba(255, 255, 255, 0.6),
      0 0 12px rgba(255, 255, 255, 0.4);
  }
  50% {
    text-shadow:
      0 0 6px rgba(255, 255, 255, 0.9),
      0 0 12px rgba(255, 255, 255, 0.7),
      0 0 18px rgba(255, 255, 255, 0.5);
  }
}

/* 按钮内的发光文本特殊处理 */
.sparkle-hover .glow-text {
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.3) !important;
}

.sparkle-hover:hover .glow-text {
  text-shadow:
    0 0 3px rgba(255, 255, 255, 0.9),
    0 0 6px rgba(255, 255, 255, 0.7),
    0 0 9px rgba(255, 255, 255, 0.5) !important;
}

/* 金色按钮文本样式 */
.batch-processor-layout .btn-primary-gold,
.batch-processor-layout .btn-primary-gold *,
.batch-processor-layout .btn-authority.btn-primary-gold,
.batch-processor-layout .btn-authority.btn-primary-gold *,
button.btn-authority.btn-primary-gold,
button.btn-authority.btn-primary-gold * {
  color: #92400e;
    text-shadow: 0 1px 3px rgba(255, 255, 255, 0.2);
  }
  
  /* 按钮悬停效果 */
  .batch-processor-layout .btn-primary-gold:hover,
  .batch-processor-layout .btn--primary-gold:hover,
  .batch-processor-layout .btn-authority.btn-primary-gold:hover,
  button.btn-authority.btn-primary-gold:hover {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    transform: translateY(-1px) scale(1.01);
    box-shadow:
      0 6px 16px rgba(245, 158, 11, 0.4),
      0 3px 8px rgba(251, 191, 36, 0.3);
}

/* 按钮内容布局优化 - 修复字体定位 */
.batch-processor-layout .btn-primary-gold > *,
.batch-processor-layout .btn--primary-gold>*,
.batch-processor-layout .btn-authority.btn-primary-gold > *,
button.btn-authority.btn-primary-gold > * {
  position: relative;
  z-index: 3;
  flex-shrink: 0;
  overflow: visible;
  text-overflow: unset;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
}

/* 修复按钮内容容器的字体定位 */
.batch-processor-layout .btn-authority.btn-primary-gold .relative.z-10,
button.btn-authority.btn-primary-gold .relative.z-10 {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  text-align: center !important;
}

/* 修复按钮内部flex容器 */
.batch-processor-layout .btn-authority.btn-primary-gold .flex.items-center.justify-center,
button.btn-authority.btn-primary-gold .flex.items-center.justify-center {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  gap: 8px !important;
}

/* 特别处理flex容器内的span元素 - 修复字体定位 */
.batch-processor-layout .btn-primary-gold span,
.batch-processor-layout .btn-authority.btn-primary-gold span,
button.btn-authority.btn-primary-gold span {
  flex-shrink: 0 !important;
  overflow: visible !important;
  text-overflow: unset !important;
  white-space: normal !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 进度指示器样式 */
.batch-processor-layout .btn-primary-gold span.bg-white.bg-opacity-20,
.batch-processor-layout .btn-authority.btn-primary-gold span.bg-white.bg-opacity-20,
button.btn-authority.btn-primary-gold span.bg-white.bg-opacity-20 {
  background: rgba(146, 64, 14, 0.15) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(146, 64, 14, 0.2) !important;
  padding: 3px 8px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  margin-left: 6px !important;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
  flex-shrink: 0 !important;
  white-space: nowrap !important;
  color: #92400e !important;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.2) !important;
}

/* 处理truncate类 */
.batch-processor-layout .btn-primary-gold .truncate,
.batch-processor-layout .btn-authority.btn-primary-gold .truncate,
button.btn-authority.btn-primary-gold .truncate {
  overflow: visible !important;
  text-overflow: unset !important;
  white-space: nowrap !important;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔗 LEGACY COMPATIBILITY - 向后兼容
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 兼容现有类名 */
.button-primary {
  @extend .btn, .btn-primary;
}

.button-secondary {
  @extend .btn, .btn-secondary;
}

.button-ghost {
  @extend .btn, .btn-ghost;
}

.enhanced-button {
  @extend .btn, .btn-md;
}

/* 通用按钮层级管理 */
.batch-processor-layout .font-medium.py-1\.5.px-3.rounded-lg,
.batch-processor-layout .btn-authority {
  position: relative;
  z-index: 4;
  isolation: isolate;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE DESIGN - 响应式设计
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 小屏幕按钮调整 */
@media (max-width: 768px) {
  /* 移动端主按钮优化 */
    .btn-primary {
      padding: 12px 20px;
      min-height: 44px;
      font-size: 14px;
      border-radius: 8px;
      margin: 8px 0;
    }
  
    /* 🗑️ 已删除：button-text 响应式样式 - 不再使用复杂层级结构 */
  
    .btn-primary .progress-badge {
      font-size: 11px;
      padding: 1px 6px;
    }
  
    .btn-primary:hover:not(:disabled) {
      transform: translateY(-1px) scale(1.01);
    }
  .batch-processor-layout .btn-authority {
    padding: var(--space-2) var(--space-3);
    min-height: 36px;
    font-size: var(--font-size-xs);
  }
  
  .batch-processor-layout .btn-authority.btn-lg {
    padding: var(--space-3) var(--space-4);
    min-height: 40px;
    font-size: var(--font-size-sm);
  }
  
  .action-button {
    width: 75px;
    height: 34px;
    font-size: 0.7rem;
  }
  
  .btn-group {
    flex-direction: column;
  }
  
  .btn-group .btn {
    border-radius: 0;
  }
  
  .btn-group .btn:first-child {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    border-bottom-left-radius: 0;
  }
  
  .btn-group .btn:last-child {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    border-top-right-radius: 0;
  }
  
  .btn-group .btn:not(:last-child) {
    border-right: 1px solid;
    border-bottom: none;
  }
  
  .icon {
    width: 14px !important;
    height: 14px !important;
  }
}

/* 14寸屏幕按钮调整 */
@media (min-width: 1366px) and (max-width: 1919px) {
  .batch-processor-layout .btn-authority {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-xs);
    min-height: 36px;
  }
  
  .batch-processor-layout .btn-authority.h-14 {
    height: 44px;
    font-size: 0.82rem;
    padding: var(--space-3) var(--space-4);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🌗 ACCESSIBILITY & PREFERENCES - 无障碍和偏好设置
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
  .btn,
  .btn-authority,
  .btn-primary-gold,
  .btn-rocket,
  .btn-loading::after,
  .sparkle-hover,
  .glow-text {
    animation: none !important;
    transition: none !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .btn,
  .btn-authority {
    border-width: 2px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .btn,
  .btn-authority,
  .action-button {
    min-height: 44px;
    min-width: 44px;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 FINAL OVERRIDES - 最终覆盖样式
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 移除这个冲突的样式块，因为它会覆盖正确的布局属性 */

/* 次要玻璃按钮样式确保 */
.btn-authority.btn-secondary-glass[class*="btn-authority"][class*="btn-secondary-glass"] {
  background: linear-gradient(135deg, #e3f2fd 0%, #f8fafc 40%, #ffffff 100%) !important;
  color: var(--color-primary-600) !important;
  border: 1px solid rgba(35, 146, 239, 0.4) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  box-shadow: 0 2px 8px rgba(35, 146, 239, 0.12), 0 1px 3px rgba(35, 146, 239, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.btn-authority.btn-secondary-glass[class*="btn-authority"][class*="btn-secondary-glass"]:hover {
  background: linear-gradient(135deg, #bbdefb 0%, #e3f2fd 30%, #ffffff 100%) !important;
  color: var(--color-primary-700) !important;
  border-color: rgba(35, 146, 239, 0.6) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 16px rgba(35, 146, 239, 0.18), 0 2px 8px rgba(35, 146, 239, 0.12), inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔥 ULTRA-SIMPLE BUTTON SYSTEM - 极简按钮系统 (彻底重构)
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 重试失败项按钮 - 浅黄色主题，与其他按钮保持一致的风格 */
.retry-failed-btn.btn--primary-gold {
  /* 尺寸与其他按钮保持一致 */
  width: auto !important;
  max-width: 200px !important;
  min-width: 120px !important;
  flex-shrink: 0 !important;

  /* 统一字体大小 - 与蓝色按钮保持一致 */
    font-size: 13px !important;
    /* 13px - 与蓝色按钮保持一致 */
  
    /* 浅黄色渐变背景 - 保持与其他按钮相似的风格 */
    background: linear-gradient(135deg,
        #fffbeb 0%,
        /* 极浅的奶黄色 */
        #fef3c7 25%,
        /* 浅黄色 */
        #fde68a 50%,
        /* 中等黄色 */
        #fcd34d 75%,
        /* 金黄色 */
        #f59e0b 100%
        /* 深金色 */
      ) !important;
  
    /* 文字颜色 - 深棕色确保可读性 */
    color: #92400e !important;
  
    /* 边框 - 与背景协调的浅黄色边框 */
    border: 1px solid rgba(245, 158, 11, 0.3) !important;
  
    /* 阴影效果 - 与其他按钮保持一致的风格 */
    box-shadow:
      0 2px 8px rgba(245, 158, 11, 0.15),
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
  
    /* 过渡动画 */
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

/* 重试失败项按钮悬停效果 */
.retry-failed-btn.btn--primary-gold:hover {
  background: linear-gradient(135deg,
      #fef3c7 0%,
        /* 浅黄色 */
        #fde68a 25%,
        /* 中等黄色 */
        #fcd34d 50%,
        /* 金黄色 */
        #f59e0b 75%,
        /* 深金色 */
        #d97706 100%
        /* 更深的金色 */
    ) !important;

  transform: translateY(-1px) scale(1.02) !important;
    box-shadow:
      0 4px 12px rgba(245, 158, 11, 0.25),
      0 2px 6px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
  }

/* 确保按钮内的数字徽章可见 */
.retry-failed-btn.btn--primary-gold .bg-white\/30 {
  background: rgba(146, 64, 14, 0.15) !important;
  /* 深棕色半透明背景 */
  color: #92400e !important;
  /* 深棕色文字 */
  font-weight: 600 !important;
  /* 加粗字体 */
}

/* 重试失败项按钮的数字徽章样式 */
.retry-failed-badge {
  background: rgba(146, 64, 14, 0.2) !important;
  /* 深棕色半透明背景，增加对比度 */
  color: #92400e !important;
  /* 深棕色文字 */
  border: 1px solid rgba(146, 64, 14, 0.3) !important;
  /* 添加边框增强可见性 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  /* 轻微阴影 */
  font-weight: 600 !important;
  /* 加粗字体 */
  min-width: 20px !important;
  /* 确保最小宽度 */
  text-align: center !important;
  /* 文字居中 */
}

/* 停止按钮样式优化 - 确保图标在浅蓝色背景上清晰可见 */
.stop-batch-btn {
  position: relative;
}

.stop-batch-btn .semi-icon {
  color: #f8fafc !important;
  /* 强制白色图标 */
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  /* 添加阴影增强对比度 */
}

.stop-batch-btn:hover .semi-icon {
  color: #f8fafc !important;
  /* hover时稍微亮一点的白色 */
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.4));
  /* 增强阴影 */
}
/* 主要处理按钮 - 100%宽度控制 */
.main-process-btn.btn-gold-simple {
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
  flex-shrink: 0 !important;
}

/* 🎯 极简金色按钮 - 单层结构，所有效果通过伪元素 */
.btn-gold-simple {
  /* 基础布局 */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 16px 24px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  /* 浅蓝色渐变背景 */
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 25%, #93c5fd 50%, #60a5fa 75%, #3b82f6 100%);

  /* 白色文字 - 清晰对比 */
  color: white !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(0, 0, 0, 0.3) !important;

  /* 阴影 */
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);

  /* 过渡 */
  transition: all 0.3s ease;
}

/* 确保所有子元素也是白色文字 */
.btn-gold-simple * {
  color: white !important;
}

/* 处理中状态的特殊星光效果 */
.btn-gold-simple.processing::before {
  background:
    radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 1) 1px, transparent 2px),
    radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.9) 1px, transparent 2px),
    radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 1) 1px, transparent 2px),
    radial-gradient(circle at 25% 75%, rgba(255, 255, 255, 0.8) 1px, transparent 2px),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.6) 1px, transparent 2px);
  animation: enhanced-sparkle 1s ease-in-out infinite;
}

/* 处理中状态的进度指示器 */
.btn-gold-simple.processing::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border-radius: 10px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%);
  animation: processing-sweep 2s linear infinite;
  z-index: 3;
}

/* 星光效果 - 通过::before伪元素 */
.btn-gold-simple::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 0.8) 1px, transparent 2px),
    radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.7) 1px, transparent 2px),
    radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.8) 1px, transparent 2px),
    radial-gradient(circle at 25% 75%, rgba(255, 255, 255, 0.6) 1px, transparent 2px);
  animation: sparkle 3s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

/* 高光扫过效果 - 通过::after伪元素 */
.btn-gold-simple::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  transform: translateX(-100%) translateY(-100%) rotate(45deg);
  transition: transform 0.8s ease;
  pointer-events: none;
  z-index: 2;
}

/* 悬停效果 */
.btn-gold-simple:hover {
  background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 25%, #60a5fa 50%, #3b82f6 75%, #2563eb 100%);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px) scale(1.01);
}

/* 悬停时的高光扫过 */
.btn-gold-simple:hover::after {
  transform: translateX(100%) translateY(100%) rotate(45deg);
}

/* 处理中状态 - 增强版 */
.btn-gold-simple.processing {
  /* 更明显的背景色 */
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 25%, #60a5fa 50%, #93c5fd 75%, #bfdbfe 100%) !important;

  /* 增强的阴影和发光效果 */
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.6),
    0 4px 16px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;

  /* 脉冲动画 */
  animation: enhanced-processing-pulse 1.5s ease-in-out infinite;

  /* 确保文字清晰可见 */
  color: white !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
}

/* 禁用状态 */
.btn-gold-simple:disabled {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #9ca3af !important;
  cursor: not-allowed;
  box-shadow: 0 2px 4px rgba(156, 163, 175, 0.1);
  transform: none;
}

.btn-gold-simple:disabled * {
  color: #9ca3af !important;
}

/* 进度徽章样式 */
.btn-gold-simple .progress-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  margin-left: 4px;
}

/* 星光动画 */
@keyframes sparkle {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* 增强的处理中脉冲动画 */
@keyframes enhanced-processing-pulse {
  0%, 100% {
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.6),
      0 4px 16px rgba(59, 130, 246, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow:
      0 0 30px rgba(59, 130, 246, 0.8),
      0 6px 20px rgba(59, 130, 246, 0.6),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
    transform: scale(1.02);
  }
}

/* 增强的星光动画 */
@keyframes enhanced-sparkle {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

/* 处理中扫过动画 */
@keyframes processing-sweep {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 原始处理中脉冲动画（保留兼容性） */
@keyframes processing-pulse {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
  }
  50% {
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎛️ MODERN SWITCH COMPONENT - 现代开关组件
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 视图模式切换器 */
.view-mode-switch {
  display: inline-flex;
  align-items: center;
}

.switch-container {
  display: flex;
  background: #f1f5f9;
  border-radius: 12px;
  padding: 4px;
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
}

.switch-option {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 8px;
  border: none;
  background: transparent;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
}

.switch-option:hover {
  color: #475569;
  background: rgba(176, 211, 249, 0.773);
}

.switch-option.active {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 25%, #87ceeb 50%, #87ceeb 75%, #87ceeb 100%);
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.switch-option.active svg {
  color: white;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 STATUS FILTER BUTTONS - 状态筛选按钮
 * ═══════════════════════════════════════════════════════════════════════════ */

/* "全部"按钮 - 使用主题金色 */
.status-filter-btn-all {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #64748b;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.status-filter-btn-all:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
}

.status-filter-btn-all.active {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 25%, #87ceeb 50%, #87ceeb 75%, #87ceeb 100%); 
   border-color: #87ceeb;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.status-filter-btn-all .count-badge {
  background: rgba(100, 116, 139, 0.1);
  color: #64748b;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  transition: all 0.2s ease;
}

.status-filter-btn-all.active .count-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}
