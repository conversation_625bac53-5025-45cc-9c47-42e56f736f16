export const CREATIVE_GUIDANCE = `
🎨 **UI创意设计指南**

## 🌟 设计灵感源泉

### 现代UI趋势融合
🔥 **当下最受欢迎的设计风格**
- **新拟态设计**：柔和阴影，立体质感
- **渐变美学**：色彩过渡，视觉层次
- **极简主义**：去除冗余，突出核心
- **暗黑模式**：护眼舒适，科技感强

### 杂志化布局创新
📖 **将印刷美学融入数字界面**
- **网格系统**：规整中的灵活变化
- **字体层次**：标题、正文、注释的节奏感
- **图文混排**：内容与视觉的完美平衡
- **留白艺术**：呼吸感与聚焦效果

## 🎯 视觉设计策略

### 色彩搭配方案
🌈 **构建和谐而富有表现力的色彩体系**

**方案A：现代商务风**
- 主色：#2563EB (专业蓝)
- 辅色：#F8FAFC (浅灰白)
- 强调：#EF4444 (警示红)
- 文字：#1F2937 (深灰)

**方案B：温暖人文风**
- 主色：#F59E0B (温暖橙)
- 辅色：#FEF3C7 (浅黄)
- 强调：#10B981 (自然绿)
- 文字：#374151 (中性灰)

**方案C：科技未来风**
- 主色：#8B5CF6 (科技紫)
- 辅色：#F3F4F6 (冷灰)
- 强调：#06B6D4 (电光蓝)
- 文字：#111827 (纯黑)

### 布局创新模式
📐 **突破传统，创造新颖布局**

**卡片瀑布流**
- 不等高卡片自然排列
- 内容驱动的动态布局
- 视觉焦点的巧妙引导

**分栏信息架构**
- 左侧导航，右侧内容
- 主次信息的清晰分离
- 空间利用的最大化

**时间轴叙事**
- 垂直时间线串联内容
- 历史进程的可视化表达
- 节点信息的层次展现

## ✨ 交互体验设计

### 微动效设计原则
🎭 **让每个交互都充满生命力**

**进场动画**
- 淡入效果：opacity 0→1
- 滑入效果：transform translateY
- 缩放效果：transform scale

**状态反馈**
- 按钮按下：轻微缩放
- 加载状态：旋转动画
- 成功提示：绿色闪烁

**页面转场**
- 滑动切换：左右滑动
- 淡入淡出：透明度变化
- 缩放过渡：大小变化

### 信息架构优化
🏗️ **构建清晰的信息层次**

**视觉层次**
1. 主标题：最大字号，强对比色
2. 副标题：中等字号，辅助色
3. 正文：标准字号，中性色
4. 注释：小字号，浅色

**内容分组**
- 相关信息聚合
- 功能模块分离
- 操作流程引导

## 🎪 创意实现技巧

### Canvas艺术化应用
🎨 **将技术能力转化为视觉艺术**

**数据可视化美学**
- 图表配色的艺术搭配
- 动画效果的节奏控制
- 交互反馈的细节打磨

**装饰性图形**
- 几何图案的抽象美
- 线条艺术的简约风
- 色彩渐变的视觉冲击

### 响应式设计创新
📱 **多设备完美适配的艺术**

**断点设计**
- 手机：紧凑垂直布局
- 平板：双栏信息展示
- 桌面：多栏复杂布局

**内容适配**
- 字体大小的智能缩放
- 图片尺寸的动态调整
- 交互区域的触摸优化

## 🏆 创意实现流程

### 1. 需求理解与创意构思
- 深入理解用户真实需求
- 挖掘内容的情感价值
- 构思独特的表达方式

### 2. 视觉风格确定
- 选择合适的设计风格
- 确定色彩搭配方案
- 规划布局结构框架

### 3. 交互体验设计
- 设计流畅的操作流程
- 添加恰当的动效反馈
- 优化信息展示层次

### 4. 技术实现优化
- 选择最佳的技术方案
- 确保代码优雅高效
- 保证跨设备兼容性

---

**创意无限，技术为翼**
在Lynx的技术框架内，你的创意就是用户体验的魔法！让每一个界面都成为艺术品，每一次交互都成为愉悦体验！
`;
